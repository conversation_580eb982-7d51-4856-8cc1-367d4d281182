import { Inject, Injectable } from '@nestjs/common';
import {
    AddressQuery,
    UpdateUserAddress,
    CreateUserAddress,
    ExploreRequest,
} from '@repo/types';
import { AddressRespository } from './address.repository';
import { tencentReverseGeocodeService } from './address.TencentMap.api';
import { CACHE_SERVICE, IAdvancedCacheService } from 'src/common/cache';
import { GeocodeRequest } from '@repo/types';
import { ParentInfo } from '@repo/types';
import { DbType } from 'src/common/database/db';
import { DB } from 'src/common/database/database.provider';
import { sql, count } from 'drizzle-orm';
import { userAddresses } from 'src/common/database/schema/addresses';

// 城市和上级城市和省份的缓存key
const CACHE_KEY_PREFIX = 'address:geocode:address';

@Injectable()
export class AddressService {
    @Inject(DB)
    private readonly db: DbType;

    @Inject()
    private readonly addressRepository: AddressRespository;
    @Inject(CACHE_SERVICE)
    private readonly cacheService: IAdvancedCacheService;

    findAll(query: AddressQuery) {
        return this.addressRepository.find(query);
    }

    updateAddress(id: string, data: UpdateUserAddress) {
        return this.addressRepository.updateAddress(id, data);
    }

    createAddress(data: CreateUserAddress) {
        return this.addressRepository.createAddress(data);
    }

    async deleteAddress(id: string) {
        await this.addressRepository.deleteAddress(id);
        return '删除成功';
    }

    getAddressByUserId(id: string) {
        return this.addressRepository.findByUserId(id);
    }

    /**
     * 根据城市名称获取它的上级城市和省份
     * @param query GeocodeRequest
     * @returns 上级城市和省份信息
     */
    async getCityParentInfo(query: GeocodeRequest) {
        try {
            const cache = await this.cacheService.hGet<ParentInfo>(
                CACHE_KEY_PREFIX,
                query.address,
            );
            if (cache) {
                return cache;
            }

            const response = await tencentReverseGeocodeService.geocode(query);

            const result: ParentInfo = {
                province: response.result.address_components.province,
                city: response.result.address_components.city,
                district: response.result.address_components.district,
                location: {
                    lng: response.result.location.lng,
                    lat: response.result.location.lat,
                },
            };

            if (result) {
                await this.cacheService.hSet<ParentInfo>(
                    CACHE_KEY_PREFIX,
                    query.address,
                    result,
                );
            }
            return result;
        } catch (e) {
            console.error(e);
        }
    }

    /**
     * 搜索城市(省/市/县(区))
     */
    async districtSearch(keyword: string) {
        const response = await tencentReverseGeocodeService.districtSearch({
            keyword,
        });
        return response.result.filter((filterItem) => filterItem.level <= 3);
    }

    /**
     * 周边推荐（explore）
     * 只需提供搜索中心点及半径，即可搜索获取周边高热度地点
     * 一般用于发送位置、地点签到等场景，自动为用户提供备选地点列表
     *
     * @param params 周边推荐请求参数
     * @returns 周边推荐结果
     * @throws BadRequestException 参数验证失败
     * @throws InternalServerErrorException API调用失败
     */
    async explore({ lat, lng }: { lat: number; lng: number }) {
        // 排除第一个数据，因为第一个数据当前用户所在的城市
        const explortAddress = (
            await tencentReverseGeocodeService.explore({
                boundary: {
                    lat,
                    lng,
                    radius: 1000,
                    auto_extend: true,
                },
                orderby: '_distance',
                policy: '1',
                location_mode: '0',
                page_size: 20,
                page_index: 1,
                output: 'json',
            })
        ).data.filter((_, index) => index !== 0);

        // 从userAddress数据表中根据周边地点的邻居数量
        const addressLocation = explortAddress.map((data) => data.location);

        // 为每个周边地点计算邻居数量
        const exploreWithNeighborCount = await Promise.all(
            explortAddress.map(async (place) => {
                const { lat, lng } = place.location;

                // 使用PostGIS ST_DWithin函数查询500米范围内的用户地址数量
                // 确保两个几何对象都有相同的SRID (4326)
                const neighborCountResult = await this.db
                    .select({
                        count: count(),
                    })
                    .from(userAddresses)
                    .where(
                        sql`ST_DWithin(
                            ST_SetSRID(geom, 4326),
                            ST_SetSRID(ST_MakePoint(${lng}, ${lat}), 4326),
                            500
                        )`,
                    );

                return {
                    ...place,
                    neighborCount: neighborCountResult[0]?.count || 0,
                };
            }),
        );

        return exploreWithNeighborCount;
    }
}
