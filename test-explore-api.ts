import { tencentReverseGeocodeService } from './apps/backend/src/modules/address/address.TencentMap.api';
import { ExploreRequest } from '@repo/types';

/**
 * 测试周边推荐API的实现
 */
async function testExploreAPI() {
    console.log('🚀 开始测试腾讯地图周边推荐API...\n');

    // 测试用例1: 基本的周边推荐请求
    const basicRequest: ExploreRequest = {
        boundary: {
            lat: 40.040394,
            lng: 116.273523,
            radius: 1000,
            auto_extend: true
        },
        policy: '1', // 地点签到场景
        page_size: 10,
        page_index: 1
    };

    console.log('📍 测试用例1: 基本周边推荐');
    console.log('请求参数:', JSON.stringify(basicRequest, null, 2));

    try {
        const result = await tencentReverseGeocodeService.explore(basicRequest);
        console.log('✅ 请求成功!');
        console.log(`📊 返回结果数量: ${result.count}`);
        console.log(`🏢 前3个POI:`, result.data.slice(0, 3).map(poi => ({
            title: poi.title,
            address: poi.address,
            category: poi.category,
            distance: poi._distance
        })));
    } catch (error) {
        console.error('❌ 请求失败:', error);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // 测试用例2: 带分类筛选的周边推荐
    const filteredRequest: ExploreRequest = {
        boundary: {
            lat: 39.908823,
            lng: 116.397470, // 天安门附近
            radius: 500,
            auto_extend: false
        },
        filter: {
            categories: ['美食', '购物']
        },
        policy: '2', // 位置共享场景
        address_format: 'short',
        page_size: 5
    };

    console.log('📍 测试用例2: 带分类筛选的周边推荐');
    console.log('请求参数:', JSON.stringify(filteredRequest, null, 2));

    try {
        const result = await tencentReverseGeocodeService.explore(filteredRequest);
        console.log('✅ 请求成功!');
        console.log(`📊 返回结果数量: ${result.count}`);
        console.log(`🏪 筛选后的POI:`, result.data.map(poi => ({
            title: poi.title,
            category: poi.category,
            distance: poi._distance
        })));
    } catch (error) {
        console.error('❌ 请求失败:', error);
    }

    console.log('\n🎉 测试完成!');
}

// 运行测试
if (require.main === module) {
    testExploreAPI().catch(console.error);
}

export { testExploreAPI };
