# 腾讯地图周边推荐（Explore）API 使用指南

## 概述

周边推荐API提供了一个强大的功能，只需提供搜索中心点及半径（无须关键词），即可搜索获取周边高热度地点。该API特别适用于：

- 发送位置场景
- 地点签到功能
- 位置共享功能
- 自动为用户提供备选地点列表

## 功能特点

✅ **类型安全**: 使用TypeScript和Zod进行完整的类型检查和验证  
✅ **结构化参数**: 将复杂的字符串参数拆分为易用的对象结构  
✅ **完整错误处理**: 提供详细的错误信息和异常处理  
✅ **参数验证**: 自动验证经纬度、半径等参数的有效性  
✅ **灵活筛选**: 支持按分类筛选POI结果  

## API接口

### 导入

```typescript
import { tencentReverseGeocodeService } from './address.TencentMap.api';
import { ExploreRequest, ExploreResponse } from '@repo/types';
```

### 方法签名

```typescript
async explore(params: ExploreRequest): Promise<ExploreResponse>
```

## 请求参数

### ExploreRequest

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| `boundary` | `ExploreBoundary` | ✅ | - | 搜索边界参数 |
| `filter` | `ExploreFilter` | ❌ | - | 筛选条件 |
| `policy` | `'1' \| '2'` | ❌ | `'1'` | 搜索策略 |
| `orderby` | `'_distance'` | ❌ | - | 按距离排序 |
| `location_mode` | `'0' \| '1'` | ❌ | `'0'` | POI坐标模式 |
| `address_format` | `'short'` | ❌ | - | 地址格式 |
| `page_size` | `number` | ❌ | `10` | 每页条目数(1-20) |
| `page_index` | `number` | ❌ | `1` | 页码 |
| `output` | `'json' \| 'jsonp'` | ❌ | `'json'` | 返回格式 |
| `callback` | `string` | ❌ | - | JSONP回调函数 |

### ExploreBoundary

| 参数 | 类型 | 必填 | 范围 | 说明 |
|------|------|------|------|------|
| `lat` | `number` | ✅ | -90 ~ 90 | 搜索中心点纬度 |
| `lng` | `number` | ✅ | -180 ~ 180 | 搜索中心点经度 |
| `radius` | `number` | ✅ | 200 ~ 1000 | 搜索半径(米) |
| `auto_extend` | `boolean` | ❌ | - | 是否自动扩大范围 |

### ExploreFilter

| 参数 | 类型 | 必填 | 限制 | 说明 |
|------|------|------|------|------|
| `categories` | `string[]` | ❌ | 最多5个 | 分类筛选列表 |

## 响应结果

### ExploreResponse

| 字段 | 类型 | 说明 |
|------|------|------|
| `status` | `number` | 状态码，0为正常 |
| `message` | `string` | 状态说明 |
| `count` | `number` | 搜索结果总数 |
| `request_id` | `string` | 请求唯一标识 |
| `data` | `ExplorePoi[]` | POI数据数组 |

### ExplorePoi

| 字段 | 类型 | 说明 |
|------|------|------|
| `id` | `string` | POI唯一标识 |
| `title` | `string` | POI名称 |
| `address` | `string` | 地址 |
| `category` | `string` | POI分类 |
| `location` | `{lat: number, lng: number}` | 坐标 |
| `_distance` | `number` | 距离(米) |
| `ad_info` | `ExploreAdInfo` | 行政区划信息 |

## 使用示例

### 基本用法

```typescript
const request: ExploreRequest = {
    boundary: {
        lat: 40.040394,
        lng: 116.273523,
        radius: 1000,
        auto_extend: true
    }
};

try {
    const result = await tencentReverseGeocodeService.explore(request);
    console.log(`找到 ${result.count} 个周边地点`);
    result.data.forEach(poi => {
        console.log(`${poi.title} - ${poi.category} (${poi._distance}米)`);
    });
} catch (error) {
    console.error('请求失败:', error);
}
```

### 带分类筛选

```typescript
const request: ExploreRequest = {
    boundary: {
        lat: 39.908823,
        lng: 116.397470,
        radius: 500
    },
    filter: {
        categories: ['美食', '购物', '酒店']
    },
    policy: '2', // 位置共享场景
    page_size: 20
};

const result = await tencentReverseGeocodeService.explore(request);
```

### 高级配置

```typescript
const request: ExploreRequest = {
    boundary: {
        lat: 31.230416,
        lng: 121.473701,
        radius: 800,
        auto_extend: false
    },
    filter: {
        categories: ['地铁站', '公交站']
    },
    policy: '1',
    orderby: '_distance',
    location_mode: '1',
    address_format: 'short',
    page_size: 15,
    page_index: 1
};

const result = await tencentReverseGeocodeService.explore(request);
```

## 错误处理

API提供了完整的错误处理机制：

```typescript
try {
    const result = await tencentReverseGeocodeService.explore(request);
    // 处理成功结果
} catch (error) {
    if (error instanceof BadRequestException) {
        // 参数验证失败或API返回错误
        console.error('请求参数错误:', error.message);
    } else if (error instanceof InternalServerErrorException) {
        // 网络错误或服务异常
        console.error('服务异常:', error.message);
    } else {
        // 其他未知错误
        console.error('未知错误:', error);
    }
}
```

## 注意事项

1. **半径限制**: 搜索半径必须在200-1000米之间
2. **分类数量**: 筛选分类最多支持5个
3. **结果限制**: 单次最多返回200条数据
4. **坐标系统**: 使用GCJ02坐标系
5. **频率限制**: 请遵守腾讯地图API的调用频率限制

## 常见分类

常用的POI分类包括：
- 美食、购物、酒店、景点
- 地铁站、公交站、停车场
- 医院、学校、银行、ATM
- 加油站、汽车服务、汽车销售
- 生活服务、休闲娱乐

更多分类请参考[腾讯地图POI分类表](https://lbs.qq.com/service/webService/webServiceGuide/webServiceAppendix)。
